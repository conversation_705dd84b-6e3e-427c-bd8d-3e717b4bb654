{"expo": {"name": "<PERSON><PERSON> Tracker Pro", "slug": "bakery-tracker", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "automatic", "splash": {"resizeMode": "contain", "backgroundColor": "#6200EE"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "This app uses camera to capture supplier bills and product images.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to save and retrieve bill images."}}, "android": {"permissions": ["android.permission.CAMERA", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE"]}, "plugins": [["expo-camera", {"cameraPermission": "Allow Bakery Tracker to access your camera to capture bills and product images."}], ["expo-media-library", {"photosPermission": "Allow <PERSON>y Tracker to access your photos to save and retrieve bill images.", "savePhotosPermission": "Allow <PERSON>y Tracker to save bill images to your photo library."}]]}}