# 🧁 Bakery Tracker Pro - React Native Edition

A comprehensive bakery management solution built with React Native and Expo, featuring complete business operations from recipe management to point-of-sale transactions.

## 🚀 Features

### 🔐 Authentication & User Management
- **Role-based Access**: Admin and Staff user roles
- **Demo Credentials**:
  - Admin: `<EMAIL>` / `admin123`
  - Staff: `<EMAIL>` / `staff123`
- **Persistent Login**: Automatic session management
- **Theme Support**: Light/Dark mode with persistence

### 📊 Dashboard & Analytics
- **Real-time Analytics**: Daily production, revenue, cost, and profit tracking
- **Interactive Charts**: Weekly revenue and production trends
- **Low Stock Alerts**: Visual warnings for ingredients below threshold
- **Quick Actions**: Fast access to key features

### 💰 Point of Sale (POS) System
- **Product Selection**: Grid view with categories and search
- **Shopping Cart**: Add, remove, modify quantities
- **Customer Management**: Name and phone tracking
- **Payment Methods**: Cash, Card, UPI support
- **Receipt Generation**: PDF invoices with sharing
- **Discount System**: Percentage-based discounts

### 🧁 Recipe & Product Management
- **Recipe Creation**: Multi-ingredient recipes with scaling
- **Product Categories**: Cakes, Pastries, Breads, Beverages
- **Ingredient Management**: Stock levels, pricing, units
- **Cost Calculation**: Automatic per-unit cost computation
- **Popular Items**: Marking and highlighting

### 📈 Production Tracking
- **Production Recording**: Track quantities produced
- **Automatic Stock Deduction**: Based on recipe requirements
- **Production History**: Chronological entries with analytics
- **Cost Analysis**: Production cost vs revenue tracking

### 📦 Inventory Management
- **Real-time Stock Levels**: Current quantities display
- **Low Stock Alerts**: Visual warnings and notifications
- **Stock Adjustments**: Manual quantity updates
- **Inventory Valuation**: Total stock value calculation
- **Threshold Management**: Minimum stock level settings

### 💵 Pricing Management
- **Cost Analysis**: Ingredient cost breakdown
- **Profit Margin Calculation**: Real-time profitability analysis
- **Price Suggestions**: 30%, 40%, 50% margin recommendations
- **Pricing History**: Track price changes over time

## 🛠 Technology Stack

- **Framework**: React Native with Expo SDK 48
- **Navigation**: React Navigation v6 (Stack & Bottom Tab)
- **State Management**: React Context API
- **Storage**: AsyncStorage for local persistence
- **Charts**: React Native Chart Kit
- **UI Components**: Custom themed components with Material Icons
- **PDF Generation**: Expo Print for receipts and reports
- **Camera**: Expo Camera for bill capture (planned)

## 📱 App Structure

```
bakery-tracker/
├── App.js                 # Main entry point
├── context/               # State management
│   ├── AuthContext.js     # Authentication state
│   ├── DataContext.js     # App data state
│   └── ThemeContext.js    # Theme management
├── navigation/            # Navigation structure
│   └── AppNavigator.js    # Main navigation
├── screens/               # All app screens
│   ├── LoginScreen.js     # Authentication
│   ├── DashboardScreen.js # Analytics dashboard
│   ├── ProductSelectionScreen.js # POS product selection
│   ├── CartScreen.js      # Shopping cart
│   ├── AddProductScreen.js # Recipe creation
│   ├── ProductionScreen.js # Production tracking
│   ├── StockManagerScreen.js # Inventory management
│   ├── PricingScreen.js   # Pricing analysis
│   └── ...               # Additional screens
├── components/            # Reusable components
│   ├── AnalyticsCard.js   # Dashboard metrics
│   ├── ProductCard.js     # Product display
│   ├── CartItem.js        # Cart item component
│   └── ProductionModal.js # Quick production entry
└── utils/                 # Utilities
    └── calculations.js    # Mathematical calculations
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Expo Go app on your mobile device

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bakery-tracker
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npx expo start
   ```

4. **Run on device**
   - Scan the QR code with Expo Go (Android) or Camera app (iOS)
   - Or press `a` for Android emulator, `i` for iOS simulator

### Demo Usage

1. **Login**: Use demo credentials (<EMAIL> / admin123)
2. **Explore Dashboard**: View analytics and quick actions
3. **Add Products**: Create recipes with ingredients
4. **Record Production**: Track what you've made
5. **Use POS**: Sell products and generate receipts
6. **Manage Stock**: Monitor and update inventory

## 📊 Key Mathematical Models

### Cost Calculation
```javascript
// Recipe cost per unit
const costPerUnit = recipe.ingredients.reduce((total, ingredient) => {
  const costPerUnit = ingredient.price;
  const amountUsed = ingredient.amount / recipe.servings;
  return total + (costPerUnit * amountUsed);
}, 0);
```

### Profit Analysis
```javascript
// Profit margin calculation
const profit = sellingPrice - cost;
const margin = (profit / sellingPrice) * 100;
```

### Stock Management
```javascript
// Stock deduction during production
recipe.ingredients.forEach(ingredient => {
  const perUnit = ingredient.amount / recipe.servings;
  ingredient.stock -= perUnit * quantityProduced;
});
```

## 🎨 Features Highlights

- **Responsive Design**: Works on all screen sizes
- **Dark/Light Theme**: Automatic and manual theme switching
- **Offline Capable**: Local storage with AsyncStorage
- **Real-time Updates**: Live data synchronization
- **Professional UI**: Material Design principles
- **Role-based Navigation**: Different interfaces for admin/staff

## 🔮 Future Enhancements

- **Firebase Integration**: Cloud data synchronization
- **Bill Capture**: Camera-based expense tracking
- **Advanced Analytics**: More detailed reporting
- **Barcode Scanning**: Product identification
- **Multi-location Support**: Chain management
- **Supplier Management**: Vendor tracking

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.

---

**Built with ❤️ for bakery owners and managers worldwide**
